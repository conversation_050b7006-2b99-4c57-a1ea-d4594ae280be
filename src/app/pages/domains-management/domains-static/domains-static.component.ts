import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SettingsService, SwuiGridField, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { takeUntil } from 'rxjs/operators';

import { DOMAIN_TYPES, DomainSearchFilters, StaticDomain } from '../../../common/models/domain.model';
import { DomainsBaseComponent } from '../domains-base/domains-base.component';
import { DomainsManagementService } from '../domains-management.service';
import { SCHEMA_LIST } from './schema';

@Component({
  selector: 'domains-static',
  templateUrl: './domains-static.component.html',
})
export class DomainsStaticComponent extends DomainsBaseComponent implements OnInit {
  @Input() searchFilters: DomainSearchFilters = {};

  schema: Swui<PERSON><PERSON><PERSON><PERSON>[] = SCHEMA_LIST;
  domainType = DOMAIN_TYPES.static;

  constructor( service: DomainsManagementService,
               settingsService: SettingsService,
               dialog: MatDialog,
               notifications: SwuiNotificationsService,
               translate: TranslateService ) {
    super(service, settingsService, dialog, notifications, translate);
  }

  ngOnInit() {
    super.ngOnInit();
    // Override the data loading to filter for static domains only
    this.service.getStaticDomains('static')
      .pipe(takeUntil(this._destroyed$))
      .subscribe((val: StaticDomain[]) => {
        this.data = val;
      });
  }
}
