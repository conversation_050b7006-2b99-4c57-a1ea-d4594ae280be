.search-filters-container {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.search-field {
  min-width: 200px;
  flex: 1;
}

.clear-filters-btn {
  height: 56px; // Match the height of mat-form-field
  margin-left: 8px;
}

@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-field {
    min-width: unset;
  }
  
  .clear-filters-btn {
    margin-left: 0;
    margin-top: 8px;
  }
}
