<div class="search-filters-container">
  <div class="search-filters">
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>{{ 'DOMAINS.searchDomain' | translate }}</mat-label>
      <input 
        matInput 
        [(ngModel)]="filters.domain" 
        (ngModelChange)="onFilterChange()" 
        placeholder="{{ 'DOMAINS.searchDomainPlaceholder' | translate }}">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
    
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>{{ 'DOMAINS.searchDescription' | translate }}</mat-label>
      <input 
        matInput 
        [(ngModel)]="filters.description" 
        (ngModelChange)="onFilterChange()" 
        placeholder="{{ 'DOMAINS.searchDescriptionPlaceholder' | translate }}">
    </mat-form-field>
    
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>{{ 'DOMAINS.searchProvider' | translate }}</mat-label>
      <input 
        matInput 
        [(ngModel)]="filters.provider" 
        (ngModelChange)="onFilterChange()" 
        placeholder="{{ 'DOMAINS.searchProviderPlaceholder' | translate }}">
    </mat-form-field>
    
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>{{ 'DOMAINS.filterStatus' | translate }}</mat-label>
      <mat-select [(ngModel)]="filters.status" (ngModelChange)="onFilterChange()">
        <mat-option value="">{{ 'COMMON.all' | translate }}</mat-option>
        <mat-option value="active">{{ 'DOMAINS.statusActive' | translate }}</mat-option>
        <mat-option value="suspended">{{ 'DOMAINS.statusSuspended' | translate }}</mat-option>
      </mat-select>
    </mat-form-field>
    
    <button 
      mat-raised-button 
      color="primary" 
      (click)="onClearFilters()" 
      class="clear-filters-btn">
      {{ 'COMMON.clearFilters' | translate }}
    </button>
  </div>
</div>
