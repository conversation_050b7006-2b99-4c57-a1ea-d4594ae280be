import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { DomainsSearchComponent } from './domains-search.component';

describe('DomainsSearchComponent', () => {
  let component: DomainsSearchComponent;
  let fixture: ComponentFixture<DomainsSearchComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DomainsSearchComponent],
      imports: [
        FormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatButtonModule,
        MatIconModule,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DomainsSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit filtersChange and search events on filter change', () => {
    spyOn(component.filtersChange, 'emit');
    spyOn(component.search, 'emit');

    component.filters = { domain: 'test.com' };
    component.onFilterChange();

    expect(component.filtersChange.emit).toHaveBeenCalledWith({ domain: 'test.com' });
    expect(component.search.emit).toHaveBeenCalledWith({ domain: 'test.com' });
  });

  it('should clear filters and emit events', () => {
    spyOn(component.filtersChange, 'emit');
    spyOn(component.clear, 'emit');

    component.filters = { domain: 'test.com', status: 'active' };
    component.onClearFilters();

    expect(component.filters).toEqual({});
    expect(component.filtersChange.emit).toHaveBeenCalledWith({});
    expect(component.clear.emit).toHaveBeenCalled();
  });

  it('should initialize with empty filters', () => {
    expect(component.filters).toEqual({});
  });

  it('should accept input filters', () => {
    const testFilters = { domain: 'example.com', status: 'active' };
    component.filters = testFilters;
    
    expect(component.filters).toEqual(testFilters);
  });
});
