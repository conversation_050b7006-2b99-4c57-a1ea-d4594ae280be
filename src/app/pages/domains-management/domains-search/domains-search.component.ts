import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DomainSearchFilters } from '../../../common/models/domain.model';

@Component({
  selector: 'domains-search',
  templateUrl: './domains-search.component.html',
  styleUrls: ['./domains-search.component.scss']
})
export class DomainsSearchComponent {
  @Input() filters: DomainSearchFilters = {};
  @Output() filtersChange = new EventEmitter<DomainSearchFilters>();
  @Output() search = new EventEmitter<DomainSearchFilters>();
  @Output() clear = new EventEmitter<void>();

  onFilterChange() {
    this.filtersChange.emit(this.filters);
    this.search.emit(this.filters);
  }

  onClearFilters() {
    this.filters = {};
    this.filtersChange.emit(this.filters);
    this.clear.emit();
  }
}
