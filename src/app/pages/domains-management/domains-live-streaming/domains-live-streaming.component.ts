import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SettingsService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { StaticDomain, DomainSearchFilters, DOMAIN_TYPES } from '../../../common/models/domain.model';
import { DomainsBaseComponent } from '../domains-base/domains-base.component';
import { DomainsManagementService } from '../domains-management.service';
import { liveStreamingDomainsSchema } from './schema';

@Component({
  selector: 'domains-live-streaming',
  templateUrl: '../domains-static/domains-static.component.html', // Reuse static template
})
export class DomainsLiveStreamingComponent extends DomainsBaseComponent implements OnInit, OnDestroy {
  @Input() searchFilters: DomainSearchFilters = {};

  constructor(
    protected service: DomainsManagementService,
    private settingsService: SettingsService,
    private dialog: MatDialog,
    protected notifications: SwuiNotificationsService,
    protected translate: TranslateService
  ) {
    super(service, settingsService, dialog, notifications, translate);
    this.domainType = DOMAIN_TYPES.static;
    this.schema = liveStreamingDomainsSchema;
  }

  ngOnInit() {
    super.ngOnInit();
    // Override the data loading to filter for live-streaming domains
    this.service.getStaticDomains('live-streaming')
      .pipe(takeUntil(this._destroyed$))
      .subscribe((val: StaticDomain[]) => {
        this.data = val;
      });
  }

  ngOnDestroy() {
    super.ngOnDestroy();
  }
}
