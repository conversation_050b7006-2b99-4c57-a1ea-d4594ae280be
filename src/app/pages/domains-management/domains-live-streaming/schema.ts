import { SwuiGridField } from '@skywind-group/lib-swui';

const LIVE_STREAMING_SCHEMA: SwuiGridField[] = [
  {
    field: 'domain',
    title: 'DOMAINS.GRID.domain',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
  },
  {
    field: 'createdAt',
    title: 'DOMAINS.GRID.created',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: false,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
  {
    field: 'updatedAt',
    title: 'DOMAINS.GRID.updated',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
];

export const liveStreamingDomainsSchema = LIVE_STREAMING_SCHEMA.filter(el => el.isList).map(el => {
  el.isListVisible = true;
  return el;
});
