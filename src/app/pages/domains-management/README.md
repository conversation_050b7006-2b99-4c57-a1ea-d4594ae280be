# Domains Management Module

## Overview

The Domains Management module provides a comprehensive interface for managing different types of domains and domain pools in the sw-ubo-hub-casino application. The module is accessible via Settings > Domains in the application navigation.

## Features

### Domain Types Supported
- **Static Domains**: Basic static domain management
- **Lobby Domains**: Domains specifically for lobby functionality
- **Live Streaming Domains**: Domains for live streaming services
- **Ehub Domains**: Domains for Ehub integration
- **Dynamic Domains**: Dynamically managed domains

### Domain Pool Management
- **Static Domain Pools**: Groups of static domains
- **Dynamic Domain Pools**: Groups of dynamic domains

### Search and Filtering
- Search by domain URL/path
- Filter by description text
- Filter by provider name
- Filter by status (Active/Suspended)

## Architecture

### Components Structure

```
domains-management/
├── domains-management.component.ts          # Main container component
├── domains-management.service.ts            # Core service for API communication
├── domains-search/                          # Search and filter component
│   ├── domains-search.component.ts
│   ├── domains-search.component.html
│   └── domains-search.component.scss
├── domains-static/                          # Static domains tab
├── domains-lobby/                           # Lobby domains tab
├── domains-live-streaming/                  # Live streaming domains tab
├── domains-ehub/                           # Ehub domains tab
├── domains-dynamic/                        # Dynamic domains tab
├── domains-pool/                           # Static domain pools tab
├── domains-dynamic-pool/                   # Dynamic domain pools tab
├── domains-base/                           # Base component for shared functionality
├── domains-item-dialog/                    # Domain creation/editing dialog
└── confirmation-dialog/                    # Confirmation dialog for deletions
```

### Data Models

The module uses comprehensive TypeScript interfaces defined in `src/app/common/models/domain.model.ts`:

- `DomainType`: Union type for all domain types
- `DomainStatus`: 'active' | 'suspended'
- `StaticDomain`, `DynamicDomain`, `LobbyDomain`: Specific domain interfaces
- `StaticDomainPool`, `DynamicDomainPool`: Domain pool interfaces
- `DomainSearchFilters`: Search and filter criteria

### Service Layer

The `DomainsManagementService` provides:
- CRUD operations for all domain types
- Search and filtering capabilities
- Cache management
- Error handling and retry logic

## Usage

### Navigation
Access the domains management page via:
Settings > Domains

### Tab Structure
The page contains 7 tabs organized as follows:

**Domain Management (5 tabs):**
1. Static Domains
2. Lobby Domains
3. Live Streaming Domains
4. Ehub Domains
5. Dynamic Domains

**Domain Pool Management (2 tabs):**
6. Domain Pools (Static)
7. Dynamic Domain Pools

### Search and Filtering
Each domain management tab includes a search component that allows filtering by:
- Domain URL/path (text search)
- Description (text search)
- Provider (text search)
- Status (dropdown: All/Active/Suspended)

### CRUD Operations
- **Create**: Add new domains or domain pools via the "+" button
- **Read**: View domains in a paginated grid with sorting
- **Update**: Edit existing domains via the edit action
- **Delete**: Remove domains with confirmation dialog

## API Integration

The module integrates with the following API endpoints:

### Static Domains
- `GET /domains/static` - List static domains with filtering
- `POST /domains/static` - Create static domain
- `PATCH /domains/static/:id` - Update static domain
- `DELETE /domains/static/:id` - Delete static domain

### Dynamic Domains
- `GET /domains/dynamic` - List dynamic domains
- `POST /domains/dynamic` - Create dynamic domain
- `PATCH /domains/dynamic/:id` - Update dynamic domain
- `DELETE /domains/dynamic/:id` - Delete dynamic domain

### Lobby Domains
- `GET /domains/lobby` - List lobby domains
- `POST /domains/lobby` - Create lobby domain
- `PATCH /domains/lobby/:id` - Update lobby domain
- `DELETE /domains/lobby/:id` - Delete lobby domain

### Domain Pools
- `GET /domain-pools/static` - List static domain pools
- `POST /domain-pools/static` - Create static domain pool
- `PATCH /domain-pools/static/:id` - Update static domain pool
- `DELETE /domain-pools/static/:id` - Delete static domain pool

- `GET /domain-pools/dynamic` - List dynamic domain pools
- `POST /domain-pools/dynamic` - Create dynamic domain pool
- `PATCH /domain-pools/dynamic/:id` - Update dynamic domain pool
- `DELETE /domain-pools/dynamic/:id` - Delete dynamic domain pool

## Error Handling

The module implements comprehensive error handling:
- Loading states with spinner indicators
- Error notifications for failed operations
- Graceful degradation when API calls fail
- Retry mechanisms for transient failures

## Testing

Unit tests are provided for:
- Main domains management component
- Search component functionality
- Service layer methods
- Error handling scenarios

Run tests with:
```bash
npm test -- --include="**/domains-management/**/*.spec.ts"
```

## Permissions

The module requires the following permissions:
- `PERMISSIONS_LIST.DOMAIN` - Basic domain management access
- Additional permissions may be required for specific operations

## Future Enhancements

Potential improvements for future releases:
- Bulk operations for multiple domains
- Import/export functionality
- Advanced filtering options
- Domain health monitoring
- Automated domain validation
- Integration with external DNS providers

## Troubleshooting

### Common Issues

1. **Loading Issues**: Check network connectivity and API endpoint availability
2. **Permission Errors**: Verify user has required domain management permissions
3. **Search Not Working**: Ensure search filters are properly formatted
4. **Data Not Refreshing**: Clear browser cache or use the refresh button

### Debug Mode

Enable debug logging by setting localStorage item:
```javascript
localStorage.setItem('debug', 'domains:*');
```

## Contributing

When contributing to this module:
1. Follow the existing component architecture patterns
2. Add unit tests for new functionality
3. Update this documentation for significant changes
4. Ensure proper error handling and loading states
5. Test with different user permission levels
