import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of, throwError } from 'rxjs';
import { DomainsManagementComponent } from './domains-management.component';
import { DomainsManagementService } from './domains-management.service';
import { DomainsPoolService } from './domains-pool/domains-pool.service';
import { GameServerService } from '../game-server/game-server.service';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('DomainsManagementComponent', () => {
  let component: DomainsManagementComponent;
  let fixture: ComponentFixture<DomainsManagementComponent>;
  let mockDomainsService: jasmine.SpyObj<DomainsManagementService>;
  let mockPoolService: jasmine.SpyObj<DomainsPoolService>;
  let mockGameServerService: jasmine.SpyObj<GameServerService>;
  let mockNotifications: jasmine.SpyObj<SwuiNotificationsService>;

  beforeEach(async () => {
    const domainsServiceSpy = jasmine.createSpyObj('DomainsManagementService', [
      'getStaticDomains', 'getLobbyDomains', 'getDynamicDomains', 'clearCache'
    ]);
    const poolServiceSpy = jasmine.createSpyObj('DomainsPoolService', ['getList']);
    const gameServerServiceSpy = jasmine.createSpyObj('GameServerService', ['getList']);
    const notificationsSpy = jasmine.createSpyObj('SwuiNotificationsService', ['error', 'success']);

    await TestBed.configureTestingModule({
      declarations: [DomainsManagementComponent],
      imports: [
        MatTabsModule,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        { provide: DomainsManagementService, useValue: domainsServiceSpy },
        { provide: DomainsPoolService, useValue: poolServiceSpy },
        { provide: GameServerService, useValue: gameServerServiceSpy },
        { provide: SwuiNotificationsService, useValue: notificationsSpy },
        { provide: MatDialog, useValue: {} },
        { provide: TranslateService, useValue: {} }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DomainsManagementComponent);
    component = fixture.componentInstance;
    mockDomainsService = TestBed.inject(DomainsManagementService) as jasmine.SpyObj<DomainsManagementService>;
    mockPoolService = TestBed.inject(DomainsPoolService) as jasmine.SpyObj<DomainsPoolService>;
    mockGameServerService = TestBed.inject(GameServerService) as jasmine.SpyObj<GameServerService>;
    mockNotifications = TestBed.inject(SwuiNotificationsService) as jasmine.SpyObj<SwuiNotificationsService>;
  });

  beforeEach(() => {
    // Setup default mock returns
    mockDomainsService.getStaticDomains.and.returnValue(of([]));
    mockDomainsService.getLobbyDomains.and.returnValue(of([]));
    mockDomainsService.getDynamicDomains.and.returnValue(of([]));
    mockGameServerService.getList.and.returnValue(of([]));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with loading state', () => {
    expect(component.isLoading).toBeFalse();
    component.ngOnInit();
    expect(component.isLoading).toBeTrue();
  });

  it('should load initial data successfully', () => {
    const mockStaticDomains = [{ id: '1', domain: 'test.com', type: 'static' }];
    const mockLobbyDomains = [{ id: '2', domain: 'lobby.com', type: 'lobby' }];
    const mockDynamicDomains = [{ id: '3', domain: 'dynamic.com', type: 'dynamic' }];
    const mockGameServers = [{ name: 'server1' }, { name: 'server2' }];

    mockDomainsService.getStaticDomains.and.returnValue(of(mockStaticDomains as any));
    mockDomainsService.getLobbyDomains.and.returnValue(of(mockLobbyDomains as any));
    mockDomainsService.getDynamicDomains.and.returnValue(of(mockDynamicDomains as any));
    mockGameServerService.getList.and.returnValue(of(mockGameServers as any));

    component.ngOnInit();

    expect(component.staticDomains).toEqual(mockStaticDomains as any);
    expect(component.lobbyDomains).toEqual(mockLobbyDomains as any);
    expect(component.dynamicDomains).toEqual(mockDynamicDomains as any);
    expect(component.gameServers).toEqual(['server1', 'server2']);
    expect(component.isLoading).toBeFalse();
  });

  it('should handle errors during data loading', () => {
    const error = new Error('Network error');
    mockDomainsService.getStaticDomains.and.returnValue(throwError(error));
    mockDomainsService.getLobbyDomains.and.returnValue(of([]));
    mockDomainsService.getDynamicDomains.and.returnValue(of([]));
    mockGameServerService.getList.and.returnValue(of([]));

    component.ngOnInit();

    expect(mockNotifications.error).toHaveBeenCalledWith('Failed to load static domains');
    expect(component.isLoading).toBeFalse();
  });

  it('should handle search filters', () => {
    const filters = { domain: 'test.com', status: 'active' };
    
    component.onSearch(filters);
    
    expect(component.searchFilters).toEqual(filters);
  });

  it('should clear search filters', () => {
    component.searchFilters = { domain: 'test.com', status: 'active' };
    
    component.onClearSearch();
    
    expect(component.searchFilters).toEqual({});
  });

  it('should update tab state on tab change', () => {
    const mockEvent = { index: 1 } as any;
    
    component.onTabChange(mockEvent);
    
    expect(component.currentTab).toBe('lobby');
    expect(component.selectedPanel).toBe('domain');
  });
});
