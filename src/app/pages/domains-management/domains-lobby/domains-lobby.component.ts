import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SettingsService, SwuiGridField, SwuiNotificationsService } from '@skywind-group/lib-swui';

import { Domain, DOMAIN_TYPES, DomainSearchFilters, LobbyDomain } from '../../../common/models/domain.model';
import { DomainsBaseComponent } from '../domains-base/domains-base.component';
import { DomainsManagementService } from '../domains-management.service';
import { SCHEMA_LIST } from './schema';
import { switchMap, takeUntil, tap } from 'rxjs/operators';

@Component({
  selector: 'domains-lobby',
  templateUrl: './domains-lobby.component.html',
})
export class DomainsLobbyComponent extends DomainsBaseComponent implements OnInit {
  @Input() searchFilters: DomainSearchFilters = {};

  schema: SwuiGridField[] = SCHEMA_LIST;
  domainType = DOMAIN_TYPES.lobby;

  constructor(service: DomainsManagementService,
              settingsService: SettingsService,
              dialog: MatDialog,
              notifications: SwuiNotificationsService,
              translate: TranslateService) {
    super(service, settingsService, dialog, notifications, translate);
  }

  ngOnInit() {
    super.ngOnInit();
    // Override the data loading to use lobby domains service
    this.service.getLobbyDomains()
      .pipe(takeUntil(this._destroyed$))
      .subscribe((val: LobbyDomain[]) => {
        this.data = val;
      });
  }

  protected setRowActions() {
    super.setRowActions();
    this.rowActions.push(new RowAction({
      title: 'DOMAINS.GRID.activeEnable',
      icon: 'edit',
      fn: (item: Domain) => {
        this.service.update(item.id, { isActive: true }, this.domainType).pipe(
          switchMap((domain) => this.translate.get('DOMAINS.notificationModified', {domain: domain.domain})),
          tap((message) => this.notifications.success(message)),
          switchMap(() => this.service.getList(this.domainType, undefined, true)),
          takeUntil(this._destroyed$)
        ).subscribe((data) => {
          this.data = data;
        });
      },
      availableFn: (row: Domain) => !row.isActive,
    }));
    this.rowActions.push(new RowAction({
      title: 'DOMAINS.GRID.activeDisable',
      icon: 'edit',
      fn: (item: Domain) => {
        this.service.update(item.id, { isActive: false }, this.domainType).pipe(
          switchMap((domain) => this.translate.get('DOMAINS.notificationModified', {domain: domain.domain})),
          tap((message) => this.notifications.success(message)),
          switchMap(() => this.service.getList(this.domainType, undefined, true)),
          takeUntil(this._destroyed$)
        ).subscribe((data) => {
          this.data = data;
        });
      },
      availableFn: (row: Domain) => row.isActive,
    }));
  }
}
