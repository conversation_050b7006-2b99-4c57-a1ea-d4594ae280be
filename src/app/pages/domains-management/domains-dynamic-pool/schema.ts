import { SwuiGridField } from '@skywind-group/lib-swui';

const DYNAMIC_POOL_SCHEMA: SwuiGridField[] = [
  {
    field: 'name',
    title: 'DOMAINS.GRID.poolName',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
  },
  {
    field: 'domains',
    title: 'DOMAINS.GRID.domainsCount',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    td: {
      type: 'custom',
      template: 'domainsCount'
    }
  },
  {
    field: 'inherited',
    title: 'DOMAINS.GRID.inherited',
    type: 'boolean',
    isList: true,
    isViewable: false,
    isSortable: true,
  },
  {
    field: 'createdAt',
    title: 'DOMAINS.GRID.created',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: false,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
  {
    field: 'updatedAt',
    title: 'DOMAINS.GRID.updated',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
];

export const dynamicDomainPoolsSchema = DYNAMIC_POOL_SCHEMA.filter(el => el.isList).map(el => {
  el.isListVisible = true;
  return el;
});
