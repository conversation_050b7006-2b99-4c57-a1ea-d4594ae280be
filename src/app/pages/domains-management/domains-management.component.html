<lib-swui-page-panel [title]="'DOMAINS.title'" [actions]="panelActions"></lib-swui-page-panel>
<div class="p-32">
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>{{ 'COMMON.loading' | translate }}</p>
  </div>
  <mat-tab-group *ngIf="!isLoading" (selectedTabChange)="onTabChange($event)" [animationDuration]="0">
    <!-- Domain Management Tabs -->
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.staticDomains' | translate }}</ng-template>
      <domains-search
        [(filters)]="searchFilters"
        (search)="onSearch($event)"
        (clear)="onClearSearch()">
      </domains-search>
      <domains-static [searchFilters]="searchFilters"></domains-static>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.lobbyDomains' | translate }}</ng-template>
      <domains-search
        [(filters)]="searchFilters"
        (search)="onSearch($event)"
        (clear)="onClearSearch()">
      </domains-search>
      <domains-lobby [searchFilters]="searchFilters"></domains-lobby>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.liveStreamingDomains' | translate }}</ng-template>
      <domains-search
        [(filters)]="searchFilters"
        (search)="onSearch($event)"
        (clear)="onClearSearch()">
      </domains-search>
      <domains-live-streaming [searchFilters]="searchFilters"></domains-live-streaming>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.ehubDomains' | translate }}</ng-template>
      <domains-search
        [(filters)]="searchFilters"
        (search)="onSearch($event)"
        (clear)="onClearSearch()">
      </domains-search>
      <domains-ehub [searchFilters]="searchFilters"></domains-ehub>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.dynamicDomains' | translate }}</ng-template>
      <domains-search
        [(filters)]="searchFilters"
        (search)="onSearch($event)"
        (clear)="onClearSearch()">
      </domains-search>
      <domains-dynamic [gameServers]="gameServers" [searchFilters]="searchFilters"></domains-dynamic>
    </mat-tab>

    <!-- Domain Pool Management Tabs -->
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.domainPools' | translate }}</ng-template>
      <domains-pool [staticDomains]="staticDomains" [lobbyDomains]="lobbyDomains"></domains-pool>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.dynamicDomainPools' | translate }}</ng-template>
      <domains-dynamic-pool [dynamicDomains]="dynamicDomains"></domains-dynamic-pool>
    </mat-tab>
  </mat-tab-group>
</div>
