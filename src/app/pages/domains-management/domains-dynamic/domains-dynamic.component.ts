import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SettingsService, SwuiGridField, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { takeUntil } from 'rxjs/operators';

import { DOMAIN_TYPES, DomainSearchFilters, DynamicDomain } from '../../../common/models/domain.model';
import { DomainsBaseComponent } from '../domains-base/domains-base.component';
import { DomainsManagementService } from '../domains-management.service';
import { SCHEMA_LIST } from './schema';

@Component({
  selector: 'domains-dynamic',
  templateUrl: './domains-dynamic.component.html',
})
export class DomainsDynamicComponent extends DomainsBaseComponent implements OnInit {
  @Input() searchFilters: DomainSearchFilters = {};

  schema: Swu<PERSON><PERSON><PERSON><PERSON><PERSON>[] = SCHEMA_LIST;
  domainType = DOMAIN_TYPES.dynamic;

  constructor( service: DomainsManagementService,
               settingsService: SettingsService,
               dialog: MatDialog,
               notifications: SwuiNotificationsService,
               translate: TranslateService ) {
    super(service, settingsService, dialog, notifications, translate);
  }

  ngOnInit() {
    super.ngOnInit();
    // Override the data loading to use dynamic domains service
    this.service.getDynamicDomains()
      .pipe(takeUntil(this._destroyed$))
      .subscribe((val: DynamicDomain[]) => {
        this.data = val;
      });
  }
}
