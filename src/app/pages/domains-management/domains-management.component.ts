import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { forkJoin, of, Subject } from 'rxjs';
import { catchError, filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import {
  Domain,
  DOMAIN_TYPES,
  DomainsItemDialogData,
  DomainType,
  DomainSearchFilters,
  StaticDomain,
  DynamicDomain,
  LobbyDomain
} from '../../common/models/domain.model';
import { GameServerService } from '../game-server/game-server.service';
import { DomainsItemDialogComponent } from './domains-item-dialog/domains-item-dialog.component';
import { DomainsManagementService } from './domains-management.service';
import { MatDialog } from '@angular/material/dialog';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { DomainsPoolDialogComponent, DomainsPoolDialogData } from './domains-pool/dialog/domains-pool-dialog.component';
import { DomainsPoolService } from './domains-pool/domains-pool.service';

type PanelType = 'domain' | 'static-pool' | 'dynamic-pool';
type TabType = 'static' | 'lobby' | 'live-streaming' | 'ehub' | 'dynamic' | 'domain-pools' | 'dynamic-domain-pools';

@Component({
  selector: 'domains-management',
  templateUrl: './domains-management.component.html',
  styleUrls: ['./domains-management.component.scss'],
  providers: [
    DomainsManagementService,
    GameServerService,
  ],
})
export class DomainsManagementComponent implements OnInit, OnDestroy {
  panelActions: PanelAction[] = [];
  selectedPanel: PanelType = 'domain';
  currentTab: TabType = 'static';
  domainType: DomainType = 'static';
  gameServers: string[] = [];
  staticDomains: StaticDomain[] = [];
  lobbyDomains: LobbyDomain[] = [];
  dynamicDomains: DynamicDomain[] = [];
  searchFilters: DomainSearchFilters = {};
  isLoading = false;

  private readonly _destroyed$ = new Subject();

  constructor(private readonly service: DomainsManagementService,
              private readonly poolService: DomainsPoolService,
              private readonly gameServerService: GameServerService,
              private readonly dialog: MatDialog,
              private readonly notifications: SwuiNotificationsService,
              private readonly translate: TranslateService) {
  }

  ngOnInit() {
    this.setPanelActions();
    this.loadInitialData();
  }

  private loadInitialData() {
    this.isLoading = true;

    const loadOperations = [
      this.gameServerService.getList().pipe(
        tap(items => this.gameServers = items.map(({name}) => name)),
        catchError(error => {
          this.handleError('Failed to load game servers', error);
          return of([]);
        })
      ),
      this.service.getStaticDomains('static').pipe(
        tap(items => this.staticDomains = items),
        catchError(error => {
          this.handleError('Failed to load static domains', error);
          return of([]);
        })
      ),
      this.service.getLobbyDomains().pipe(
        tap(items => this.lobbyDomains = items),
        catchError(error => {
          this.handleError('Failed to load lobby domains', error);
          return of([]);
        })
      ),
      this.service.getDynamicDomains().pipe(
        tap(items => this.dynamicDomains = items),
        catchError(error => {
          this.handleError('Failed to load dynamic domains', error);
          return of([]);
        })
      )
    ];

    // Execute all load operations in parallel
    forkJoin(loadOperations)
      .pipe(takeUntil(this._destroyed$))
      .subscribe({
        next: () => {
          this.isLoading = false;
        },
        error: (error) => {
          this.isLoading = false;
          this.handleError('Failed to load domains data', error);
        }
      });
  }

  private handleError(message: string, error: any) {
    console.error(message, error);
    this.notifications.error(message);
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  onTabChange(event: MatTabChangeEvent) {
    switch (event.index) {
      case 0: // Static Domains
        this.selectedPanel = 'domain';
        this.currentTab = 'static';
        this.domainType = DOMAIN_TYPES.static;
        break;
      case 1: // Lobby Domains
        this.selectedPanel = 'domain';
        this.currentTab = 'lobby';
        this.domainType = DOMAIN_TYPES.lobby;
        break;
      case 2: // Live Streaming Domains
        this.selectedPanel = 'domain';
        this.currentTab = 'live-streaming';
        this.domainType = DOMAIN_TYPES.static; // Live streaming uses static endpoint with type filter
        break;
      case 3: // Ehub Domains
        this.selectedPanel = 'domain';
        this.currentTab = 'ehub';
        this.domainType = DOMAIN_TYPES.static; // Ehub uses static endpoint with type filter
        break;
      case 4: // Dynamic Domains
        this.selectedPanel = 'domain';
        this.currentTab = 'dynamic';
        this.domainType = DOMAIN_TYPES.dynamic;
        break;
      case 5: // Domain Pools (Static)
        this.selectedPanel = 'static-pool';
        this.currentTab = 'domain-pools';
        break;
      case 6: // Dynamic Domain Pools
        this.selectedPanel = 'dynamic-pool';
        this.currentTab = 'dynamic-domain-pools';
        break;
      default:
        this.selectedPanel = 'domain';
        this.currentTab = 'static';
        this.domainType = DOMAIN_TYPES.static;
    }
    this.setPanelActions();
  }

  private setPanelActions() {
    if (this.selectedPanel === 'static-pool') {
      this.panelActions = [{
        title: 'DOMAINS.addPool',
        color: 'primary',
        icon: 'add',
        actionFn: () => this.openStaticPoolDialog(),
      }];
    } else if (this.selectedPanel === 'dynamic-pool') {
      this.panelActions = [{
        title: 'DOMAINS.addDynamicPool',
        color: 'primary',
        icon: 'add',
        actionFn: () => this.openDynamicPoolDialog(),
      }];
    } else {
      this.panelActions = [{
        title: 'DOMAINS.addDomain',
        color: 'primary',
        icon: 'add',
        actionFn: () => this.openDomainDialog(),
      }];
    }
  }

  private openStaticPoolDialog() {
    const data: DomainsPoolDialogData = {
      domains: this.staticDomains,
      lobbyDomains: this.lobbyDomains
    };
    const dialogRef = this.dialog.open(DomainsPoolDialogComponent, {
      width: '500px',
      data,
      disableClose: true
    });
    dialogRef.afterClosed()
      .pipe(
        filter(record => !!record),
        switchMap(record => this.poolService.create(record)),
        switchMap(pool => this.translate.get('DOMAINS.notificationPoolCreated', {name: pool.name})),
        tap(message => this.notifications.success(message)),
        takeUntil(this._destroyed$)
      )
      .subscribe(() => {
        this.poolService.isGridChanged$.next();
      });
  }

  private openDynamicPoolDialog() {
    // TODO: Implement dynamic pool dialog
    // For now, show a placeholder notification
    this.notifications.info('Dynamic Domain Pool creation will be implemented');
  }

  private openDomainDialog() {
    const data: DomainsItemDialogData = {
      gameServers: this.gameServers,
      type: this.getDomainTypeForCurrentTab()
    };
    const dialogRef = this.dialog.open(DomainsItemDialogComponent, {
      width: '500px',
      data,
      disableClose: true
    });
    dialogRef.afterClosed()
      .pipe(
        filter(domain => !!domain),
        switchMap((domain: any) => {
          // Set the correct type for static domain variants
          if (this.currentTab === 'live-streaming') {
            domain.type = 'live-streaming';
          } else if (this.currentTab === 'ehub') {
            domain.type = 'ehub';
          } else if (this.currentTab === 'static') {
            domain.type = 'static';
          }
          return this.service.create(domain, this.domainType);
        }),
        switchMap((domain: Domain) => this.translate.get('DOMAINS.notificationCreated', {domain: domain.domain})),
        tap(message => this.notifications.success(message)),
        takeUntil(this._destroyed$)
      )
      .subscribe(() => {
        this.service.isGridChanged$.next(this.domainType);
      });
  }

  private getDomainTypeForCurrentTab(): DomainType {
    switch (this.currentTab) {
      case 'lobby':
        return DOMAIN_TYPES.lobby;
      case 'dynamic':
        return DOMAIN_TYPES.dynamic;
      case 'static':
      case 'live-streaming':
      case 'ehub':
      default:
        return DOMAIN_TYPES.static;
    }
  }

  onSearch(filters: DomainSearchFilters) {
    this.searchFilters = filters;
    // Trigger refresh of current tab data with filters
    this.refreshCurrentTabData();
  }

  onClearSearch() {
    this.searchFilters = {};
    // Trigger refresh of current tab data without filters
    this.refreshCurrentTabData();
  }

  private refreshCurrentTabData() {
    // Clear cache and refresh data for current tab
    switch (this.currentTab) {
      case 'static':
        this.service.clearCache('static');
        this.service.getStaticDomains('static', undefined, true, this.searchFilters)
          .pipe(takeUntil(this._destroyed$))
          .subscribe(items => this.staticDomains = items);
        break;
      case 'lobby':
        this.service.clearCache('lobby');
        this.service.getLobbyDomains(undefined, true, this.searchFilters)
          .pipe(takeUntil(this._destroyed$))
          .subscribe(items => this.lobbyDomains = items);
        break;
      case 'dynamic':
        this.service.clearCache('dynamic');
        this.service.getDynamicDomains(undefined, true, this.searchFilters)
          .pipe(takeUntil(this._destroyed$))
          .subscribe(items => this.dynamicDomains = items);
        break;
      case 'live-streaming':
        this.service.clearCache('static');
        this.service.getStaticDomains('live-streaming', undefined, true, this.searchFilters)
          .pipe(takeUntil(this._destroyed$))
          .subscribe(items => {
            // Update the data for live streaming component
            this.service.isGridChanged$.next('static');
          });
        break;
      case 'ehub':
        this.service.clearCache('static');
        this.service.getStaticDomains('ehub', undefined, true, this.searchFilters)
          .pipe(takeUntil(this._destroyed$))
          .subscribe(items => {
            // Update the data for ehub component
            this.service.isGridChanged$.next('static');
          });
        break;
    }
  }
}
