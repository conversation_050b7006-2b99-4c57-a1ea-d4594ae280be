import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../common/directives/trim-input-value/trim-input-value.module';
import { ConfirmationDialogComponent } from './confirmation-dialog/confirmation-dialog.component';
import { DomainsDynamicComponent } from './domains-dynamic/domains-dynamic.component';
import { DomainsItemDialogComponent } from './domains-item-dialog/domains-item-dialog.component';
import { DomainsManagementComponent } from './domains-management.component';
import { DomainsStaticComponent } from './domains-static/domains-static.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DomainsPoolComponent } from './domains-pool/domains-pool.component';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DomainsPoolDialogComponent } from './domains-pool/dialog/domains-pool-dialog.component';
import { DomainsManagementService } from './domains-management.service';
import { DomainsPoolService } from './domains-pool/domains-pool.service';
import { DomainsLobbyComponent } from './domains-lobby/domains-lobby.component';
import { DomainsLiveStreamingComponent } from './domains-live-streaming/domains-live-streaming.component';
import { DomainsEhubComponent } from './domains-ehub/domains-ehub.component';
import { DomainsDynamicPoolComponent } from './domains-dynamic-pool/domains-dynamic-pool.component';
import { DomainsSearchComponent } from './domains-search/domains-search.component';

@NgModule({
  declarations: [
    DomainsManagementComponent,
    DomainsStaticComponent,
    DomainsDynamicComponent,
    DomainsLobbyComponent,
    DomainsLiveStreamingComponent,
    DomainsEhubComponent,
    DomainsPoolComponent,
    DomainsDynamicPoolComponent,
    DomainsSearchComponent,
    DomainsItemDialogComponent,
    ConfirmationDialogComponent,
    DomainsPoolDialogComponent
  ],
    exports: [
        DomainsManagementComponent,
        DomainsPoolComponent
    ],
  imports: [
    CommonModule,
    MatDialogModule,
    MatTabsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatTableModule,
    TrimInputValueModule,
    MatIconModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    FlexLayoutModule,
    SwuiPagePanelModule,
    ReactiveFormsModule,
    TranslateModule,
    SwuiGridModule,
    RouterModule.forChild([
      {
        path: '',
        pathMatch: 'full',
        component: DomainsManagementComponent,
        data: {
          title: 'Domains'
        }
      }
    ]),
    FormsModule
  ],
  providers: [
    DomainsManagementService,
    DomainsPoolService
  ]
})
export class DomainsManagementModule {
}
