import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, of, Subject, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../app.constants';
import {
  Domain,
  DOMAIN_TYPES,
  DomainRow,
  DomainType,
  DomainSearchFilters,
  DomainCreateData,
  DomainUpdateData,
  StaticDomain,
  DynamicDomain,
  LobbyDomain,
  DomainPool,
  StaticDomainPool,
  DynamicDomainPool,
  DomainPoolType,
  DOMAIN_POOL_TYPES
} from '../../common/models/domain.model';

@Injectable()
export class DomainsManagementService {
  isGridChanged$ = new Subject<DomainType>();

  private cachedItems: Record<string, Domain[]> = {};
  private cachedItem: Record<string, Domain> = {};

  constructor(private readonly http: HttpClient,
              private readonly notifications: SwuiNotificationsService) {
  }

  getList(type: DomainType, path?: string, force?: boolean, filters?: DomainSearchFilters): Observable<Domain[]> {
    const cacheKey = this.getCacheKey(type, filters);
    if (!force && this.cachedItems[cacheKey]) {
      return of(this.cachedItems[cacheKey]);
    }

    let params = new HttpParams();
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          params = params.set(key, filters[key]);
        }
      });
    }

    // For static domains, add type filter to separate Static, Lobby, Live Streaming, Ehub
    if (type === 'static' && filters?.type) {
      params = params.set('type', filters.type);
    }

    const options = params.keys().length > 0 ? { params } : {};

    return this.http.get<Domain[]>(`${this.getUrl(type, path)}`, options)
      .pipe(
        map(response => response.map(this.processRecord)),
        tap((data: Domain[]) => {
          this.cachedItems[cacheKey] = data;
        }),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  getStaticDomains(domainType: 'static' | 'lobby' | 'live-streaming' | 'ehub', path?: string, force?: boolean, filters?: DomainSearchFilters): Observable<StaticDomain[]> {
    const typeFilters = { ...filters, type: domainType };
    return this.getList('static', path, force, typeFilters) as Observable<StaticDomain[]>;
  }

  getDynamicDomains(path?: string, force?: boolean, filters?: DomainSearchFilters): Observable<DynamicDomain[]> {
    return this.getList('dynamic', path, force, filters) as Observable<DynamicDomain[]>;
  }

  getLobbyDomains(path?: string, force?: boolean, filters?: DomainSearchFilters): Observable<LobbyDomain[]> {
    return this.getList('lobby', path, force, filters) as Observable<LobbyDomain[]>;
  }

  delete(id: string, type: DomainType): Observable<Object> {
    return this.http.delete(`${this.getUrl(type)}/${id}`)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  create(data: DomainCreateData, type: DomainType): Observable<Domain> {
    const payload = { ...data };
    if (type === DOMAIN_TYPES.lobby) {
      payload['name'] = payload.domain;
      delete payload.domain;
    }
    return this.http.post<Domain>(this.getUrl(type), JSON.stringify(payload))
      .pipe(
        map(this.processRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  update(id: string, data: DomainUpdateData, type: DomainType): Observable<Domain> {
    const payload = { ...data };
    delete payload.id;
    if (type === DOMAIN_TYPES.lobby) {
      payload['name'] = payload.domain;
      delete payload.domain;
    }
    return this.http.patch<Domain>(`${this.getUrl(type)}/${id}`, JSON.stringify(payload))
      .pipe(
        map(this.processRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  bulkOperation(bulkRequestData: any[]) {
    return this.http.post(`${API_ENDPOINT}/entities/bulk-operation`, bulkRequestData).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  setEntityDomain(type: DomainType, domainId: string, path: string = ':') {
    return this.invokeEntityDomainRequest('PUT', type, path, domainId);
  }

  getEntityDomain(type: DomainType, path: string = ':', force = false) {
    if (!force) {
      if (this.cachedItem[DOMAIN_TYPES[type]]) {
        return of(this.cachedItem[DOMAIN_TYPES[type]]);
      }
    }
    return this.invokeEntityDomainRequest('GET', type, path);
  }

  removeEntityDomain(type: DomainType, path: string = ':') {
    return this.invokeEntityDomainRequest('DELETE', type, path);
  }

  // Domain Pool Management Methods
  getDomainPools(type: DomainPoolType, path?: string, force?: boolean): Observable<DomainPool[]> {
    const cacheKey = `pools_${type}`;
    if (!force && this.cachedItems[cacheKey]) {
      return of(this.cachedItems[cacheKey]);
    }

    return this.http.get<DomainPool[]>(`${this.getDomainPoolUrl(type, path)}`)
      .pipe(
        map(response => response.map(this.processDomainPoolRecord)),
        tap((data: DomainPool[]) => {
          this.cachedItems[cacheKey] = data;
        }),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  getStaticDomainPools(path?: string, force?: boolean): Observable<StaticDomainPool[]> {
    return this.getDomainPools('static', path, force) as Observable<StaticDomainPool[]>;
  }

  getDynamicDomainPools(path?: string, force?: boolean): Observable<DynamicDomainPool[]> {
    return this.getDomainPools('dynamic', path, force) as Observable<DynamicDomainPool[]>;
  }

  createDomainPool(data: any, type: DomainPoolType): Observable<DomainPool> {
    return this.http.post<DomainPool>(this.getDomainPoolUrl(type), JSON.stringify(data))
      .pipe(
        map(this.processDomainPoolRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  updateDomainPool(id: string, data: any, type: DomainPoolType): Observable<DomainPool> {
    return this.http.patch<DomainPool>(`${this.getDomainPoolUrl(type)}/${id}`, JSON.stringify(data))
      .pipe(
        map(this.processDomainPoolRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  deleteDomainPool(id: string, type: DomainPoolType): Observable<Object> {
    return this.http.delete(`${this.getDomainPoolUrl(type)}/${id}`)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  private invokeEntityDomainRequest(method: string, type: DomainType, path: string = ':', domainId?: string) {
    let url = `${this.getEntityDomainUrl(path)}${type}`;
    url = (method === 'PUT') ? `${url}/${domainId}` : url;

    return this.http.request(method, url).pipe(
      map(this.processRecord),
      tap((data: Domain) => {
        this.cachedItem[DOMAIN_TYPES[type]] = data;
      }),
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  private processRecord(record: DomainRow): Domain {
    for (const property of ['createdAt', 'updatedAt']) {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    }
    return {
      ...record,
      domain: record.name || record.domain || '',
      _meta: {
        createdAt: record.createdAt && moment(record.createdAt),
        updatedAt: record.updatedAt && moment(record.updatedAt),
      }
    };
  }

  private processDomainPoolRecord(record: any): DomainPool {
    for (const property of ['createdAt', 'updatedAt']) {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    }
    return {
      ...record,
      _meta: {
        createdAt: record.createdAt && moment(record.createdAt),
        updatedAt: record.updatedAt && moment(record.updatedAt),
      }
    };
  }

  private getCacheKey(type: DomainType, filters?: DomainSearchFilters): string {
    let key = DOMAIN_TYPES[type];
    if (filters) {
      const filterKeys = Object.keys(filters).sort();
      const filterString = filterKeys.map(k => `${k}:${filters[k]}`).join('|');
      key += `_${filterString}`;
    }
    return key;
  }

  private getUrl(type: DomainType, path?: string): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/domains/${type}`;
  }

  private getDomainPoolUrl(type: DomainPoolType, path?: string): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/domain-pools/${type}`;
  }

  private getEntityDomainUrl(path?: string): string {
    return `${API_ENDPOINT}/${path && path !== ':' ? path : ''}/entitydomain/`;
  }

  // Clear cache methods
  clearCache(type?: DomainType): void {
    if (type) {
      Object.keys(this.cachedItems).forEach(key => {
        if (key.startsWith(DOMAIN_TYPES[type])) {
          delete this.cachedItems[key];
        }
      });
    } else {
      this.cachedItems = {};
    }
  }

  clearDomainPoolCache(type?: DomainPoolType): void {
    if (type) {
      delete this.cachedItems[`pools_${type}`];
    } else {
      Object.keys(this.cachedItems).forEach(key => {
        if (key.startsWith('pools_')) {
          delete this.cachedItems[key];
        }
      });
    }
  }
}
